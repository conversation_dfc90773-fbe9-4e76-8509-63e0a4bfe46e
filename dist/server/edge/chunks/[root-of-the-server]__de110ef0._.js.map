{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  // Pass through API routes without authentication\n  if (request.nextUrl.pathname.startsWith('/api')) {\n    return NextResponse.next();\n  }\n\n  // Auto-sync theme CSS files in development mode\n  if (process.env.NODE_ENV === 'development') {\n    // Check if this is a request for theme CSS\n    if (request.nextUrl.pathname.startsWith('/themes/') && request.nextUrl.pathname.endsWith('.css')) {\n      console.log('🎨 Theme CSS requested:', request.nextUrl.pathname);\n    }\n  }\n\n  const { pathname } = request.nextUrl;\n  const token = request.cookies.get('firebaseIdToken')?.value;\n\n  // --- FIX 1: Add the homepage '/' to the list of public routes ---\n  const publicRoutes = ['/', '/login'];\n  \n  // Private routes\n  const privateRoutes = ['/dashboard', '/portfolio'];\n\n  // This logic is now more robust. It checks if the current path is one of the private/public routes.\n  const isPrivateRoute = privateRoutes.some(route => pathname.startsWith(route));\n  const isPublicRoute = publicRoutes.includes(pathname); // Using .includes() is better for exact matches like '/'\n\n  // If user is not authenticated and tries to access a private route, redirect to login\n  if (isPrivateRoute && !token) {\n    const loginUrl = new URL('/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // If user IS authenticated and tries to access a public route (now including the homepage), redirect to dashboard\n  if (isPublicRoute && token) {\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  return NextResponse.next();\n}\n\n// Middleware matcher configuration\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,iDAAiD;IACjD,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS;QAC/C,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,gDAAgD;IAChD,wCAA4C;QAC1C,2CAA2C;QAC3C,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS;YAChG,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,OAAO,CAAC,QAAQ;QACjE;IACF;IAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IACpC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEtD,mEAAmE;IACnE,MAAM,eAAe;QAAC;QAAK;KAAS;IAEpC,iBAAiB;IACjB,MAAM,gBAAgB;QAAC;QAAc;KAAa;IAElD,oGAAoG;IACpG,MAAM,iBAAiB,cAAc,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;IACvE,MAAM,gBAAgB,aAAa,QAAQ,CAAC,WAAW,yDAAyD;IAEhH,sFAAsF;IACtF,IAAI,kBAAkB,CAAC,OAAO;QAC5B,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,kHAAkH;IAClH,IAAI,iBAAiB,OAAO;QAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}