
import { ProfolifyThemeProps } from "@/lib/types";
import Hero from "./components/Hero";
import About from "./components/About";
import Experience from "./components/Experience";
import Skills from "./components/Skills";
import Education from "./components/Education";

export default function NurseTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  if (!serverData && !isEditing) {
    return <div className="w-full h-screen flex items-center justify-center">Loading Theme...</div>;
  }

  return (
    <div className="font-sans antialiased">
      <Hero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
      <About isEditing={isEditing} serverData={serverData} />
      <Experience isEditing={isEditing} serverData={serverData} />
      <Skills isEditing={isEditing} serverData={serverData} />
      <Education isEditing={isEditing} serverData={serverData} />
    </div>
  );
}
