
import { EditorContext } from "@/contexts/EditorContext";
import { AnyPortfolioData } from "@/lib/types";
import { useContext } from "react";
import Hero from "./components/Hero";
import About from "./components/About";

interface ThemeProps {
  isEditing: boolean;
  serverData?: AnyPortfolioData;
}

export default function NurseTheme({ isEditing, serverData }: ThemeProps) {
  const { state } = useContext(EditorContext);
  const data = isEditing ? state.formData : serverData;

  if (!data) {
    return <div className="w-full h-screen flex items-center justify-center">Loading Theme...</div>;
  }

  return (
    <div className="font-sans antialiased">
      <Hero isEditing={isEditing} data={data} />
      <About isEditing={isEditing} data={data} />
      {/* You can add more profession-specific sections here */}
      {/* e.g., <Experience isEditing={isEditing} data={data} /> */}
      {/* e.g., <Education isEditing={isEditing} data={data} /> */}
    </div>
  );
}
