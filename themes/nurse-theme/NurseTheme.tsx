
import { ProfolifyThemeProps } from "@/lib/types";
import Hero from "./components/Hero";
import About from "./components/About";

export default function NurseTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  if (!serverData && !isEditing) {
    return <div className="w-full h-screen flex items-center justify-center">Loading Theme...</div>;
  }

  return (
    <div className="font-sans antialiased">
      <Hero isEditing={isEditing} serverData={serverData} />
      <About isEditing={isEditing} serverData={serverData} />
      {/* You can add more profession-specific sections here */}
      {/* e.g., <Experience isEditing={isEditing} serverData={serverData} /> */}
      {/* e.g., <Education isEditing={isEditing} serverData={serverData} /> */}
    </div>
  );
}
