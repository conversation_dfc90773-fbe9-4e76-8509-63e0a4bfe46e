
import { EditableText } from "@/components/custom-ui/EditableText";
import { useEditorContext } from "@/contexts/EditorContext";
import { AnyPortfolioData, isMedicalPortfolio } from "@/lib/types";

interface AboutProps {
  isEditing: boolean;
  data: AnyPortfolioData;
}

export default function About({ isEditing, data }: AboutProps) {
  const { dispatch } = useEditorContext();

  const handleUpdate = (field: string, value: any) => {
    dispatch({
      type: 'UPDATE_FORM_DATA',
      payload: { [field]: value },
    });
  };

  // This component is designed for nurses, so we use a type guard.
  if (!isMedicalPortfolio(data)) {
    // In a real-world scenario, you might show a message in the editor
    // if the wrong portfolio type is used with this theme.
    return isEditing ? <div className="p-4 text-red-500">This theme requires a Healthcare portfolio type.</div> : null;
  }

  return (
    <section className="w-full bg-white py-16 md:py-24">
      <div className="container mx-auto px-4 grid md:grid-cols-3 gap-8">
        
        {/* Bio Section */}
        <div className="md:col-span-2">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">About Me</h2>
          <div className="text-gray-600 leading-relaxed">
            <EditableText
              isEditing={isEditing}
              value={data.bio}
              onChange={(value) => handleUpdate('bio', value)}
              placeholder="Write a brief summary about your experience, passion, and career goals as a nurse."
              textarea
            />
          </div>
        </div>

        {/* Professional Information Section */}
        <div className="md:col-span-1 bg-gray-50 p-6 rounded-lg">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Credentials</h3>
          
          {/* Medical License */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-500 mb-1">License Number</label>
            <EditableText
              isEditing={isEditing}
              value={data.medicalLicense || ''}
              onChange={(value) => handleUpdate('medicalLicense', value)}
              placeholder="e.g., RN123456"
              className="w-full text-gray-900"
            />
          </div>

          {/* Specializations */}
          <div className="mb-4">
            <h4 className="block text-sm font-medium text-gray-500 mb-2">Specializations</h4>
            {isEditing ? (
              <EditableText
                isEditing={isEditing}
                value={(data.specializations || []).join(', ')}
                onChange={(value) => handleUpdate('specializations', value.split(',').map(s => s.trim()))}
                placeholder="e.g., Pediatrics, Oncology"
                className="w-full text-gray-900"
                errorMessage="Separate with commas"
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {(data.specializations || []).map(spec => (
                  <span key={spec} className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded-full">{spec}</span>
                ))}
              </div>
            )}
          </div>

          {/* Certifications */}
          <div>
            <h4 className="block text-sm font-medium text-gray-500 mb-2">Certifications</h4>
             {isEditing ? (
              <EditableText
                isEditing={isEditing}
                initialValue={(data.certifications || []).join(', ')}
                onChange={(value) => handleUpdate('certifications', value.split(',').map(c => c.trim()))}
                placeholder="e.g., BLS, ACLS"
                className="w-full text-gray-900"
                errorMessage="Separate with commas"
              />
            ) : (
              <ul className="list-disc list-inside text-gray-700">
                {(data.certifications || []).map(cert => (
                  <li key={cert}>{cert}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
