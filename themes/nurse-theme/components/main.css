/* Use this file for any custom CSS that cannot be achieved with Tailwind. For most cases, this file can remain empty. */

/* Editable field styles for nurse theme */
.editable-field {
  border: 2px dotted transparent;
  border-radius: 4px;
  padding: 4px;
  transition: all 0.2s ease;
  min-height: 1.5em;
  display: inline-block;
  min-width: 100px;
}

.editable-field:hover,
.editable-field:focus {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
  outline: none;
}

.editable-field-large {
  border: 2px dotted transparent;
  border-radius: 4px;
  padding: 8px;
  transition: all 0.2s ease;
  min-height: 3em;
  display: block;
  width: 100%;
}

.editable-field-large:hover,
.editable-field-large:focus {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
  outline: none;
}

/* Error state for editable fields */
.editable-field.error,
.editable-field-large.error {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

/* Placeholder styling */
.editable-field:empty:before,
.editable-field-large:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}