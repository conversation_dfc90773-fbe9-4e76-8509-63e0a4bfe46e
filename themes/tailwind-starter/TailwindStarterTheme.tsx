
import { ProfolifyThemeProps } from "@/lib/types";
import Hero from "./components/Hero";

export default function TailwindStarterTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  if (!serverData && !isEditing) {
    return <div>Loading...</div>;
  }

  return (
    <div className="font-sans">
      <Hero isEditing={isEditing} serverData={serverData} />
      {/* You can add more sections here, for example: */}
      {/* <About isEditing={isEditing} serverData={serverData} /> */}
      {/* <Projects isEditing={isEditing} serverData={serverData} /> */}
    </div>
  );
}
