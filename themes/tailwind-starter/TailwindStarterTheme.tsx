
import { EditorContext } from "@/contexts/EditorContext";
import { AnyPortfolioData } from "@/lib/types";
import { useContext } from "react";
import Hero from "./components/Hero";

interface ThemeProps {
  isEditing: boolean;
  serverData?: AnyPortfolioData;
}

export default function TailwindStarterTheme({ isEditing, serverData }: ThemeProps) {
  const { state } = useContext(EditorContext);
  const data = isEditing ? state.formData : serverData;

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div className="font-sans">
      <Hero isEditing={isEditing} data={data} />
      {/* You can add more sections here, for example: */}
      {/* <About isEditing={isEditing} data={data} /> */}
      {/* <Projects isEditing={isEditing} data={data} /> */}
    </div>
  );
}
