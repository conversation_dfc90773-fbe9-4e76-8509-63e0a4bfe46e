import { PortfolioData, AnyPortfolioData, Project, Experience, MedicalExperience, isGeneralPortfolio, isITPortfolio, isMedicalPortfolio } from './types';

export interface ValidationError {
  field: string;
  message: string;
  section?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Phone number validation
export const validatePhoneNumber = (phone: string): boolean => {
  if (!phone || phone.trim() === '') return true; // Optional field
  
  // Remove all non-digit characters for validation
  const digitsOnly = phone.replace(/\D/g, '');
  
  // Must have 10-15 digits (international format)
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    return false;
  }
  
  // Basic format validation - should contain mostly digits, spaces, dashes, parentheses, plus
  const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,20}$/;
  return phoneRegex.test(phone.trim());
};

// Email validation
export const validateEmail = (email: string): boolean => {
  if (!email || email.trim() === '') return false; // Required field
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// URL validation
export const validateUrl = (url: string): boolean => {
  if (!url || url.trim() === '') return true; // Optional field
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Validate required text fields
export const validateRequiredText = (text: string | undefined, minLength: number = 1): boolean => {
  return !!(text && text.trim().length >= minLength);
};

// Validate project
export const validateProject = (project: Project, index: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  const section = `Project ${index + 1}`;
  
  if (!validateRequiredText(project.title)) {
    errors.push({
      field: `projects[${index}].title`,
      message: 'Project title is required',
      section
    });
  }
  
  if (!validateRequiredText(project.description)) {
    errors.push({
      field: `projects[${index}].description`,
      message: 'Project description is required',
      section
    });
  }

  // Image validation - required for projects with content
  if (!project.imageUrl || project.imageUrl.trim() === '') {
    errors.push({
      field: `projects[${index}].imageUrl`,
      message: 'Project image is required',
      section
    });
  }

  // URLs are optional, but if provided must be valid
  if (project.url && !validateUrl(project.url)) {
    errors.push({
      field: `projects[${index}].url`,
      message: 'Invalid project URL format',
      section
    });
  }
  
  if (project.liveUrl && !validateUrl(project.liveUrl)) {
    errors.push({
      field: `projects[${index}].liveUrl`,
      message: 'Invalid live URL format',
      section
    });
  }
  
  return errors;
};

// Validate experience
export const validateExperience = (experience: Experience, index: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  const section = `Experience ${index + 1}`;
  
  if (!validateRequiredText(experience.role)) {
    errors.push({
      field: `experiences[${index}].role`,
      message: 'Job title/role is required',
      section
    });
  }
  
  if (!validateRequiredText(experience.company)) {
    errors.push({
      field: `experiences[${index}].company`,
      message: 'Company name is required',
      section
    });
  }
  
  if (!validateRequiredText(experience.duration)) {
    errors.push({
      field: `experiences[${index}].duration`,
      message: 'Duration is required',
      section
    });
  }

  if (!validateRequiredText(experience.location)) {
    errors.push({
      field: `experiences[${index}].location`,
      message: 'Location is required',
      section
    });
  }

  if (!validateRequiredText(experience.description)) {
    errors.push({
      field: `experiences[${index}].description`,
      message: 'Description is required',
      section
    });
  }

  // Company URL is optional, but if provided must be valid
  if (experience.companyUrl && !validateUrl(experience.companyUrl)) {
    errors.push({
      field: `experiences[${index}].companyUrl`,
      message: 'Invalid company URL format',
      section
    });
  }
  
  return errors;
};

// Validate medical experience
export const validateMedicalExperience = (experience: MedicalExperience, index: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  const section = `Experience ${index + 1}`;

  if (!validateRequiredText(experience.role)) {
    errors.push({
      field: `experiences[${index}].role`,
      message: 'Position/role is required',
      section
    });
  }

  if (!validateRequiredText(experience.company)) {
    errors.push({
      field: `experiences[${index}].company`,
      message: 'Hospital/clinic name is required',
      section
    });
  }

  if (!validateRequiredText(experience.duration)) {
    errors.push({
      field: `experiences[${index}].duration`,
      message: 'Duration is required',
      section
    });
  }

  if (!validateRequiredText(experience.location)) {
    errors.push({
      field: `experiences[${index}].location`,
      message: 'Location is required',
      section
    });
  }

  // Description is optional for medical professionals
  // Specialization is optional
  // Hospital type is optional

  return errors;
};

// Main portfolio validation function (backward compatible)
export const validatePortfolio = (data: PortfolioData): ValidationResult => {
  return validateAnyPortfolio(data);
};

// Universal portfolio validation function
export const validateAnyPortfolio = (data: AnyPortfolioData): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  console.log('🔍 Validating portfolio data:', {
    userName: data.userName,
    profession: data.profession,
    profileImageUrl: data.profileImageUrl,
    themeCategory: data.themeCategory,
    professionType: data.professionType,
    experiencesCount: data.experiences?.length,
    skillsCount: data.skills?.length,
    // Only log projects if they exist (not all portfolio types have projects)
    ...(isGeneralPortfolio(data) || isITPortfolio(data) ? {
      projectsCount: data.projects?.length,
      projects: data.projects?.map(p => ({
        title: p.title,
        description: p.description,
        imageUrl: p.imageUrl,
        isComplete: !!(p.title && p.description && p.imageUrl)
      }))
    } : {}),
    experiences: data.experiences?.map(e => ({
      role: e.role,
      company: e.company,
      duration: e.duration,
      location: e.location,
      description: e.description,
      isComplete: !!(e.role && e.company && e.duration && e.location && e.description)
    })),
    skills: data.skills?.map(s => ({
      name: s.name,
      isEmpty: !s.name
    }))
  });
  
  // Basic required fields
  if (!validateRequiredText(data.userName)) {
    errors.push({
      field: 'userName',
      message: 'Your name is required',
      section: 'Basic Info'
    });
  }
  
  if (!validateRequiredText(data.profession)) {
    errors.push({
      field: 'profession',
      message: 'Your profession/title is required',
      section: 'Basic Info'
    });
  }
  
  // Email validation (required)
  if (!validateEmail(data.email || '')) {
    errors.push({
      field: 'email',
      message: 'A valid email address is required',
      section: 'Contact'
    });
  }
  
  // Phone validation (optional, but must be valid if provided)
  if (data.phone && !validatePhoneNumber(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Please enter a valid phone number (10-15 digits)',
      section: 'Contact'
    });
  }
  
  // Social links validation (optional, but must be valid URLs if provided)
  if (data.githubUrl && !validateUrl(data.githubUrl)) {
    errors.push({
      field: 'githubUrl',
      message: 'Please enter a valid GitHub URL',
      section: 'Social Links'
    });
  }
  
  if (data.linkedinUrl && !validateUrl(data.linkedinUrl)) {
    errors.push({
      field: 'linkedinUrl',
      message: 'Please enter a valid LinkedIn URL',
      section: 'Social Links'
    });
  }
  
  if (data.twitterUrl && !validateUrl(data.twitterUrl)) {
    errors.push({
      field: 'twitterUrl',
      message: 'Please enter a valid Twitter URL',
      section: 'Social Links'
    });
  }
  
  // Projects validation - only for portfolio types that have projects
  if (isGeneralPortfolio(data) || isITPortfolio(data)) {
    if (!data.projects || data.projects.length === 0) {
      errors.push({
        field: 'projects',
        message: 'At least one project is required',
        section: 'Projects'
      });
    } else {
      // Validate ALL projects (including empty ones)
      data.projects.forEach((project, index) => {
        const projectErrors = validateProject(project, index);
        errors.push(...projectErrors);
      });
    }
  }
  // Medical portfolios don't require projects
  
  // Experience validation - validate ALL experience cards
  if (!data.experiences || data.experiences.length === 0) {
    errors.push({
      field: 'experiences',
      message: 'At least one work experience is required',
      section: 'Experience'
    });
  } else {
    // Validate ALL experiences (including empty ones)
    data.experiences.forEach((experience, index) => {
      let experienceErrors: ValidationError[];

      // Use different validation based on portfolio type
      if (isMedicalPortfolio(data)) {
        experienceErrors = validateMedicalExperience(experience, index);
      } else {
        experienceErrors = validateExperience(experience, index);
      }

      errors.push(...experienceErrors);
    });
  }
  
  // Skills validation - at least 3 skills required
  if (!data.skills || data.skills.length === 0) {
    errors.push({
      field: 'skills',
      message: 'At least 3 skills are required',
      section: 'Skills'
    });
  } else {
    // Filter out empty skills
    const validSkills = data.skills.filter(skill => validateRequiredText(skill.name));

    if (validSkills.length < 3) {
      errors.push({
        field: 'skills',
        message: `At least 3 skills are required (currently have ${validSkills.length})`,
        section: 'Skills'
      });
    }
  }
  
  // Hero image validation - required for most portfolios, optional for medical
  if (!data.profileImageUrl || data.profileImageUrl.trim() === '') {
    if (isMedicalPortfolio(data)) {
      warnings.push({
        field: 'profileImageUrl',
        message: 'Profile image is recommended to build trust with patients',
        section: 'Hero'
      });
    } else {
      errors.push({
        field: 'profileImageUrl',
        message: 'Profile image is required',
        section: 'Hero'
      });
    }
  }

  // Bio/About validation - required
  if (!validateRequiredText(data.bio)) {
    errors.push({
      field: 'bio',
      message: 'About section is required to tell visitors about yourself',
      section: 'About'
    });
  }

  // Validate qualifications - only for portfolio types that have qualification fields
  if (isGeneralPortfolio(data) || isITPortfolio(data)) {
    const hasQualification1 = validateRequiredText(data.qualification1);
    const hasQualification2 = validateRequiredText(data.qualification2);

    if (!hasQualification1 && !hasQualification2) {
      errors.push({
        field: 'qualifications',
        message: 'At least one qualification is required (education, certification, or experience)',
        section: 'About'
      });
    }
  }

  // Medical portfolios have different validation for education/certifications
  if (isMedicalPortfolio(data)) {
    // Education validation - at least one education entry required
    if (!data.education || data.education.length === 0) {
      errors.push({
        field: 'education',
        message: 'At least one education entry is required',
        section: 'Education'
      });
    } else {
      // Validate each education entry
      data.education.forEach((edu, index) => {
        if (!validateRequiredText(edu.degree)) {
          errors.push({
            field: `education[${index}].degree`,
            message: 'Degree is required',
            section: `Education ${index + 1}`
          });
        }
        if (!validateRequiredText(edu.institution)) {
          errors.push({
            field: `education[${index}].institution`,
            message: 'Institution is required',
            section: `Education ${index + 1}`
          });
        }
        if (!validateRequiredText(edu.year)) {
          errors.push({
            field: `education[${index}].year`,
            message: 'Year is required',
            section: `Education ${index + 1}`
          });
        }
      });
    }

    // Medical license validation - optional but recommended
    if (!validateRequiredText(data.medicalLicense)) {
      warnings.push({
        field: 'medicalLicense',
        message: 'Medical license number is recommended for healthcare professionals',
        section: 'Professional Info'
      });
    }

    // Specializations validation - at least one recommended
    if (!data.specializations || data.specializations.length === 0) {
      warnings.push({
        field: 'specializations',
        message: 'At least one specialization is recommended',
        section: 'Professional Info'
      });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Helper function to get validation errors for a specific field
export const getFieldErrors = (validationResult: ValidationResult, fieldName: string): ValidationError[] => {
  return validationResult.errors.filter(error => error.field === fieldName);
};

// Helper function to check if a specific section has errors
export const sectionHasErrors = (validationResult: ValidationResult, sectionName: string): boolean => {
  return validationResult.errors.some(error => error.section === sectionName);
};
